const multer = require("multer");
const excelFilter = (req, file, cb) => {
    let fileExtension = file.originalname.split(".");
    if(fileExtension.length > 0) {
       fileExtension = fileExtension[fileExtension.length-1].toLowerCase(); 
    }
    if (
      (file.mimetype.includes("excel") ||
      file.mimetype.includes("spreadsheetml")) && fileExtension === "xlsx"
    ) {
      cb(null, true);
    } else {
      cb("Please upload only xlsx file.", false);
    }
  };

  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, __basedir + "uploads/");
    },
    filename: (req, file, cb) => {
      cb(null, `${Date.now()}-${file.originalname}`);
    },
  });


const uploadFile = multer({ storage: storage});
module.exports = uploadFile;