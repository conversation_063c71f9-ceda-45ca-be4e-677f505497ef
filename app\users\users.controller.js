﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const userService = require("./user.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.post("/auth/login", authenticate);

router.get("/", getAll);
router.post("/", userSchema, create);
router.get("/:id", getById);
router.put("/:id", userSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function authenticate(req, res, next) {
  userService
    .authenticate(req.body)
    .then((user) => {
      res.json({ status: true, result: user });
    })
    .catch(next);
}

function create(req, res, next) {
  userService
    .create(req.body)
    .then((user) => {
      logRequest(
        req,
        `Created a new User with name: "${user?.name}"`,
        "CREATE"
      );
      res.json({ status: true, message: "User created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  userService
    .getAll(req.query)
    .then((users) => {
      logRequest(req, "Fetched all Users", "READ");
      res.json(users);
    })
    .catch(next);
}

function getById(req, res, next) {
  userService
    .getById(req.params.id)
    .then((user) => {
      logRequest(req, `Fetched User with name: "${user?.name}"`, "READ");
      res.json(user);
    })
    .catch(next);
}

function userSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().optional().allow(""),
    role: Joi.string().valid("admin", "manager", "user").required(),
    status: Joi.string().valid("active", "inactive", "suspended").optional(),
    phone: Joi.string().optional().allow(""),
    designation: Joi.string().optional().allow(""),
    department: Joi.string().optional().allow(""),
    profileImage: Joi.string().optional().allow(""),
    lastLogin: Joi.date().optional().allow(null, ""),
    isEmailVerified: Joi.boolean().optional(),
    resetToken: Joi.string().optional().allow(""),
    resetTokenExpiry: Joi.date().optional().allow(null),
    deletedBy: Joi.number().optional().allow(null),
    deletedAt: Joi.date().optional().allow(null),
    isActive: Joi.boolean().optional(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(req, `Updated User with name: "${user?.name}"`, "UPDATE");
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  userService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted User with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "User deleted successfully" });
    })
    .catch(next);
}
