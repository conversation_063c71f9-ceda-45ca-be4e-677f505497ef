const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const userService = require("./user.service");

// Routes
router.post("/auth/login", authenticate);

router.get("/", getAll);
router.post("/", userSchema, create);
router.get("/:id", getById);
router.put("/:id", userSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function authenticate(req, res, next) {
  userService
    .authenticate(req.body)
    .then((user) => {
      res.json({ status: true, result: user });
    })
    .catch(next);
}

function create(req, res, next) {
  userService
    .create(req.body)
    .then((user) => {
      res.json({ status: true, message: "User created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  userService
    .getAll(req.query)
    .then((users) => {
      res.json(users);
    })
    .catch(next);
}

function getById(req, res, next) {
  userService
    .getById(req.params.id)
    .then((user) => {
      res.json(user);
    })
    .catch(next);
}

function userSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    slug: Joi.string().optional(), // Auto-generated, but allow manual override
    email: Joi.string().email().required(),
    password: Joi.string().optional().allow(""),
    role: Joi.string().valid("admin", "manager", "user").required(),
    status: Joi.string().valid("active", "inactive", "suspended").optional(),
    phone: Joi.string().optional().allow(""),
    designation: Joi.string().optional().allow(""),
    department: Joi.string().optional().allow(""),
    profileImage: Joi.string().optional().allow(""),
    lastLogin: Joi.date().optional().allow(null, ""),
    isEmailVerified: Joi.boolean().optional(),
    resetToken: Joi.string().optional().allow(""),
    resetTokenExpiry: Joi.date().optional().allow(null),
    deletedBy: Joi.number().optional().allow(null),
    deletedAt: Joi.date().optional().allow(null),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userService
    .update(req.params.id, req.body)
    .then((user) => {
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  userService
    .delete(req.params.id)
    .then(() => {
      res.json({ status: true, message: "User deleted successfully" });
    })
    .catch(next);
}
