const nodemailer = require("nodemailer");
const config = require("../../config.json");
const { convertFromRaw } = require("draft-js");
const { stateToHTML } = require("draft-js-export-html");
const { format } = require("date-fns");
const axios = require("axios");

const formatDate = (dateString) => {
  if (!dateString) return null;
  return format(new Date(dateString), "dd-MM-yyyy HH:mm");
};

// const convertDraftContentToHTML = (draftContentState) => {
//   const contentState = convertFromRaw(JSON.parse(draftContentState)); // Convert raw content to ContentState
//   return stateToHTML(contentState); // Convert ContentState to HTML
// };

const convertDraftContentToHTML = (draftContentState) => {
  // Parse raw data
  const rawContent = JSON.parse(draftContentState);

  const customStyleMap = {
    BOLD: { element: "strong" },
    ITALIC: { element: "em" },
    UNDERLINE: { element: "u" },
  };

  rawContent.blocks.forEach((block) => {
    if (block.inlineStyleRanges && block.inlineStyleRanges.length) {
      block.inlineStyleRanges.forEach((range) => {
        const style = range.style;
        // Add mapping for colors
        if (style.startsWith("color-rgb")) {
          const color = style.replace("color-", "");
          customStyleMap[style] = { style: { color } };
        }
        // Add mapping for font sizes
        if (style.startsWith("fontsize-")) {
          const fontSize = style.replace("fontsize-", "") + "px";
          customStyleMap[style] = { style: { fontSize } };
        }
      });
    }
  });

  const contentState = convertFromRaw(rawContent);
  const htmlcode = stateToHTML(contentState, { inlineStyles: customStyleMap });
  return htmlcode;
};

const sendTestEmail = async (email, subject, emailBody) => {
  const { host, port, user, password } = config.emailSettings;
  return new Promise((resolve, reject) => {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: user,
        pass: password,
      },
      debug: true, // Enable debugging
      logger: true, // Log SMTP communication
    });
    // let transporter = nodemailer.createTransport({
    //   host: host,
    //   port: port,
    //   secure: false, // true for 465, false for other ports
    //   auth: {
    //     user: user, // generated ethereal user
    //     pass: password, // generated ethereal password
    //   },
    //   debug: true, // Enable debugging
    //     logger: true, // Log SMTP communication
    // });

    const emailOptions = {
      from: "<EMAIL>", // sender address
      to: email, // list of receivers
      subject: "DoRight Subject", // Subject line
      // text: "Reset Password Text", // plain text body
      html: "This is a html body", // html body
    };

    transporter.sendMail(emailOptions, function (error, info) {
      if (error) {
        console.log("error is " + error);
        resolve(false); // or use rejcet(false) but then you will have to handle errors
      } else {
        console.log("Email sent: " + info.response);
        resolve(true);
      }
    });
  });
};

const sendEmail = async (email, subject, emailBody) => {
  const isHtml =
    typeof emailBody === "string" && emailBody.trim().startsWith("<");
  const htmlBody = isHtml ? emailBody : convertDraftContentToHTML(emailBody);
  const { host, port, user, password } = config.emailSettings;
  return new Promise((resolve, reject) => {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: user,
        pass: password,
      },
      debug: true, // Enable debugging
      logger: true, // Log SMTP communication
    });
    // let transporter = nodemailer.createTransport({
    //   host: host,
    //   port: port,
    //   secure: false, // true for 465, false for other ports
    //   auth: {
    //     user: user, // generated ethereal user
    //     pass: password, // generated ethereal password
    //   },
    //   debug: true, // Enable debugging
    //     logger: true, // Log SMTP communication
    // });

    const emailOptions = {
      from: user, // sender address
      to: email, // list of receivers
      subject: subject, // Subject line
      // text: "Reset Password Text", // plain text body
      html: htmlBody, // html body
    };
    transporter.sendMail(emailOptions, function (error, info) {
      if (error) {
        console.log("error is " + error);
        resolve(false); // or use rejcet(false) but then you will have to handle errors
      } else {
        console.log("Email sent: " + info.response);
        resolve(true);
      }
    });
  });
};

const generateRandowmPassword = () => {
  var length = 8,
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
    retVal = "";
  for (var i = 0, n = charset.length; i < length; ++i) {
    retVal += charset.charAt(Math.floor(Math.random() * n));
  }
  return retVal;
};

const generateSlug = (name) => {
  return name.toLowerCase().trim().split(" ").join("-");
};

const replacePlaceholders = (templateContent, data) => {
  return templateContent.replace(
    /{{(.*?)}}/g,
    (match, key) => data[key.trim()] || match
  );
};

const generateOtp = () => {
    if(config.environment === "PROD") {
        return Math.floor(100000 + Math.random() * 900000).toString();
    } else {
        return "888888";
    }
};

const prepareTemplate = (data) => {
  const template =
    "Hello {{fullname}},\n\n" +
    "Thank you for reaching out to us. We have received the following message from you:\n\n" +
    '"{{emailBody}}"\n\n' +
    "We will respond to your query as soon as possible.\n\n" +
    "Regards,\n" +
    "The Support Team";

  return template.replace(/{{(.*?)}}/g, (match, key) => {
    const trimmedKey = key.trim();
    return data[trimmedKey] !== undefined ? data[trimmedKey] : match;
  });
};

const haversineDistance = (lat1, lon1, lat2, lon2) => {
  const toRad = (value) => (value * Math.PI) / 180;

  const R = 6371; // Earth's radius in kilometers

  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in kilometers
};

const PERSPECTIVE_API_URL = `https://commentanalyzer.googleapis.com/v1alpha1/comments:analyze?key=${config.PERSPECTIVE_API_KEY}`;
const TOXICITY_THRESHOLD = 0.7;

const requestedAttributes = {
  TOXICITY: {},
  SEVERE_TOXICITY: {},
  INSULT: {},
  PROFANITY: {},
  THREAT: {},
  IDENTITY_ATTACK: {},
  SEXUALLY_EXPLICIT: {},
  FLIRTATION: {},
  SPAM: {},
  OBSCENE: {},
};

async function analyzeText(text) {
  const body = {
    comment: { text },
    languages: ["en"],
    requestedAttributes,
  };

  const response = await axios.post(PERSPECTIVE_API_URL, body);
  return response.data.attributeScores;
}

async function validateTextForAbuse(text) {
  const scores = await analyzeText(text);

  for (const attr in scores) {
    const value = scores[attr].summaryScore.value;
    if (value >= TOXICITY_THRESHOLD) {
      throw new Error(
        "Submitted content contains inappropriate or offensive language."
      );
    }
  }
}

module.exports = { validateTextForAbuse };

// Example usage:

module.exports = {
  sendEmail,
  generateRandowmPassword,
  generateSlug,
  replacePlaceholders,
  generateOtp,
  sendTestEmail,
  prepareTemplate,
  formatDate,
  haversineDistance,
  validateTextForAbuse,
};
