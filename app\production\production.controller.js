const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const productionService = require("./production.service");
const uploadFile = require("../_middleware/upload");

// Routes
router.get("/", getAll);
router.post("/", productionSchema, create);
router.post("/bulk-import", uploadFile.single("file"), bulkImport);
router.get("/:id", getById);
router.put("/:id", productionSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  productionService
    .create(req.body)
    .then((production) => {
      res.json({
        status: true,
        message: "Production record created successfully",
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  productionService
    .getAll(req.query)
    .then((productions) => {
      res.json(productions);
    })
    .catch(next);
}

function getById(req, res, next) {
  productionService
    .getById(req.params.id)
    .then((production) => {
      res.json(production);
    })
    .catch(next);
}

function productionSchema(req, res, next) {
  const schema = Joi.object({
    productionSheetNo: Joi.string().required(),
    quantity: Joi.number().integer().min(1).required(),
    operatorId: Joi.number().integer().required(),
    vehicleType: Joi.string().required(),
    appointmentDate: Joi.date().required(),
    status: Joi.string().optional(),
    notes: Joi.string().optional().allow(""),
    deletedBy: Joi.number().optional().allow(null),
    deletedAt: Joi.date().optional().allow(null),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  productionService
    .update(req.params.id, req.body)
    .then((production) => {
      res.json(production);
    })
    .catch(next);
}

function _delete(req, res, next) {
  productionService
    .delete(req.params.id)
    .then(() => {
      res.json({
        status: true,
        message: "Production record deleted successfully",
      });
    })
    .catch(next);
}

function bulkImport(req, res, next) {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      status: false,
      message: "No file uploaded. Please upload an Excel file.",
    });
  }

  // Get userId from headers for audit tracking
  const userId = req?.headers?.userid;
  const filePath = req.file.path;

  productionService
    .bulkImport(filePath, userId)
    .then((result) => {
      res.json({
        status: true,
        message: "Bulk import completed successfully",
        data: {
          totalRows: result.totalRows,
          insertedCount: result.insertedCount,
          skippedCount: result.skippedCount,
          errorCount: result.errorCount,
          insertedData: result.insertedData,
          skippedData: result.skippedData,
          errors: result.errors,
          rejectedFileUrl: result.rejectedFileUrl,
        },
      });
    })
    .catch((error) => {
      next(error);
    });
}
