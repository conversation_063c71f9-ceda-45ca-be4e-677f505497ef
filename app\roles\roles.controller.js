const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const roleService = require("./role.service");

// Routes
router.get("/", getAll);
router.post("/", roleSchema, create);
router.get("/:id", getById);
router.put("/:id", roleSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  roleService
    .create(req.body)
    .then((role) => {
      res.json({ status: true, message: "Role created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  roleService
    .getAll(req.query)
    .then((roles) => {
      res.json(roles);
    })
    .catch(next);
}

function getById(req, res, next) {
  roleService
    .getById(req.params.id)
    .then((role) => {
      res.json(role);
    })
    .catch(next);
}

function roleSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    status: Joi.string().valid("active", "inactive").optional(),
    description: Joi.string().optional().allow(""),
    deletedBy: Joi.number().optional().allow(null),
    deletedAt: Joi.date().optional().allow(null),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  roleService
    .update(req.params.id, req.body)
    .then((role) => {
      res.json(role);
    })
    .catch(next);
}

function _delete(req, res, next) {
  roleService
    .delete(req.params.id)
    .then(() => {
      res.json({ status: true, message: "Role deleted successfully" });
    })
    .catch(next);
}
