const requestPasswordTemplate = (name, link) => {
  return `<html>
      <head>
        <style></style>
      </head>
      <body>
        <p>Hi ${name},</p>
        <p>We're sending you the email because you requested a password reset. Click on this link to create a new password : <a href="${link}">Reset Password Link</a></p>
        <p> If you didn't request a password reset, you can ignore this email.<br />Your Password will not be changed</p>
        <p>Thanks<br />
        The MyHouser Team</p>
      </body>
    </html>`;
};

const newAccountCreationTemplate = (
  firstName,
  lastName,
  email,
  password,
  link,
  userType
) => {
  return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear ${firstName},</p>
     <p>Congratulations on your decision to join the MyHouser community. We all are about making a difference to the home ownership experience. Your decision to join this community comes out of caring and putting your clients interest front and forward.</p>
     <p>You are not just a subscriber, but you are going to be a member of this coveted community that takes care of their clients, work with them on their lifetime homeownership experience and advise them in their best interests.</p>
     <p>We the team at <a href="${link}">Myhouser.com</a> will be continuously and rigorously working on developing the best of the breed calculators, online wealth management tools that will empower the members to visualize, plan and achieve stellar results in building wealth with real estate and a combination of other financial vehicles. Because we are helping our clients in multiple aspects include Family, Finances while enjoying and building a career.</p>
     <p>Welcome to this new experience of ownership by design. Let’s collectively work together to help our clients build their dreams into reality.</p>
     <p>Please use the following credentials to login.</p>
     <p>${link}</p>
     <h3>Email Address: ${email}</h3>
     <h3>Password: ${password}</h3>
     <p>Please change password after login to the application.</p>
     <p>From Myhouser.com team,</p>
     <p>Amol Heda</p>
   </body>
 </html>`;
};

const reminderEmailTemplate = (params) => {
  return `
<html>
  <head>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        font-size: 12px;
      }
      p {
        margin: 8px 0;
      }
      a {
        color: #007bff;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <p>Hi ${params?.userRecord?.firstName} ${params?.userRecord?.lastName},</p>
    
    <p>We're sending you this email because you requested an alert.</p>
    
    <p>This is a reminder that your <strong>${params?.reminder_type}</strong> payment for 
    <strong>${params?.portfolioRecord?.addressOne}</strong> is due on 
    <strong>${params?.date}</strong>.</p>
    
    <p><strong>Payment Details:</strong></p>
    <ul>
      <li>Amount Due: $${params?.amount}</li>
      <li>Due Date: ${params?.date}</li>
      <li>Address 1: ${params?.portfolioRecord?.addressOne}</li>
    </ul>
    
    <p>Please ensure that your payment is made before the due date to avoid any late fees or penalties.</p>
    
    <p>If you have already made the payment, please disregard this reminder.</p>
    
    <p>Kindly record your <strong>${params?.reminder_type}</strong> payment transaction in the MyHouser application once paid. Follow these steps:</p>
    <ol>
      <li>Log in to your MyHouser account at <a href="https://myhouser.com" target="_blank">myhouser.com</a>.</li>
      <li>Navigate to the <strong>Transactions</strong> tab.</li>
      <li>Click on the <strong>Payments</strong> tab.</li>
      <li>Add the payment details.</li>
    </ol>
    
    <p>Thank you for keeping your records up to date!</p>
    
    <p>Best Regards,</p>
    <p>The MyHouser Team</p>
  </body>
</html>
  `;
};

const contactUsEmailTemplate = (params) => {
  return `
<html>
  <head>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        font-size: 12px;
      }
      p {
        margin: 8px 0;
      }
      a {
        color: #007bff;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    
    <p>You have received a new message through the contact form:</p>
    
    <p><strong>Contact Details:</strong></p>
    <ul>
      <li>Full Name: ${params?.name}</li>
      <li>Email: <a href="mailto:${params?.email}">${params?.email}</a></li>
    </ul>
    
    <p><strong>Message:</strong></p>
    <p>${params?.message}</p>
    
  
  </body>
</html>
  `;
};

const activationEmailTemplate = (params) => {
  return `
<html>
  <head>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        font-size: 14px;
      }
      p {
        margin: 8px 0;
      }
      a {
        color: #007bff;
        text-decoration: none;
      }
      .button {
        background-color: #007bff;
        color: white;
        padding: 10px 15px;
        text-align: center;
        border-radius: 5px;
        text-decoration: none;
        display: inline-block;
        margin-top: 15px;
      }
    </style>
  </head>
  <body>
    <p>Dear DoRight Team,</p>

    <p>We have received a request from the following NGO to claim their account:</p>

    <p><strong>NGO Details:</strong></p>
    <ul>
     
      <li><strong>Email:</strong> <a href="mailto:${params?.ngoEmail}">${params?.ngoEmail}</a></li>
      <li><strong>Contact Person:</strong> ${params?.name}</li>
    
    </ul>

    <p>Please review and verify the details. If everything is correct, you may proceed with claiming the NGO account.</p>


    <p>Thank you for your attention to this matter.</p>

    <p>Best regards,</p>
    <p>The DoRight System</p>

  </body>
</html>
  `;
};

module.exports = {
  requestPasswordTemplate,
  newAccountCreationTemplate,
  reminderEmailTemplate,
  contactUsEmailTemplate,
  activationEmailTemplate,
};
