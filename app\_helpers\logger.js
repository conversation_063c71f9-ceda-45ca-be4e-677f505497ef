const db = require("./db");

module.exports = {
  logAction,
};

async function logAction(userId, action, pageName, type) {
  try {
    // For now, just console log the action
    // You can extend this to save to a database table later
    console.log(`[${new Date().toISOString()}] User ${userId} - ${type}: ${action} on ${pageName}`);
    
    // If you want to save to database, you can create a logs table and uncomment below:
    // await db.Log.create({
    //   userId: userId,
    //   action: action,
    //   pageName: pageName,
    //   type: type,
    //   timestamp: new Date()
    // });
  } catch (error) {
    console.error("Error logging action:", error);
  }
}
