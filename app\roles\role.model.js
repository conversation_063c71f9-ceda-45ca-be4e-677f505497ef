const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    name: { type: DataTypes.STRING, allowNull: false },
    status: {
      type: DataTypes.ENUM("active", "inactive"),
      defaultValue: "active",
    },
    description: { type: DataTypes.TEXT, allowNull: true },
    deletedBy: { type: DataTypes.INTEGER, allowNull: true },
    deletedAt: { type: DataTypes.DATE, allowNull: true },
  };

  const options = {
    defaultScope: {
      attributes: {},
    },
  };

  return sequelize.define("roles", attributes, options);
}
