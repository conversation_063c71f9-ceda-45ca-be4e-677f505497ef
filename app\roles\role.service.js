const db = require("../_helpers/db");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  return await db.Role.findAll({
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  return await db.Role.create(params);
}

async function update(id, params) {
  const record = await getSingleRecord(id);
  Object.assign(record, params);
  await record.save();
  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  // Soft delete
  record.status = "inactive";
  record.deletedAt = new Date();
  await record.destroy();
}

// helper
async function getSingleRecord(id) {
  const record = await db.Role.findByPk(id);
  if (!record) throw "Role not found";
  return record;
}
