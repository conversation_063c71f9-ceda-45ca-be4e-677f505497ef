const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  return await db.Production.findAll({
    where: {},
    include: [
      {
        model: db.User,
        as: "operator",
        attributes: ["id", "name", "email", "phone", "designation"],
      },
    ],
    order: [["appointmentDate", "DESC"]],
  });
}

async function getById(id) {
  const record = await db.Production.findByPk(id, {
    include: [
      {
        model: db.User,
        as: "operator",
        attributes: ["id", "name", "email", "phone", "designation"],
      },
    ],
  });
  if (!record) throw "Production record not found";
  return record;
}

async function create(params) {
  // Validate that operator exists
  const operator = await db.User.findByPk(params.operatorId);
  if (!operator) throw "Operator not found";

  return await db.Production.create(params);
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Validate that operator exists if operatorId is being updated
  if (params.operatorId) {
    const operator = await db.User.findByPk(params.operatorId);
    if (!operator) throw "Operator not found";
  }

  Object.assign(record, params);
  await record.save();
  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  // Soft delete
  record.status = "cancelled";
  record.deletedAt = new Date();
  await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect operator names from Excel and generate slugs
    const operatorNamesInSheet = rows
      .map((row) => row.Operator || row.operator || row["Operator Name"])
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const operatorSlugsInSheet = operatorNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Debug: Log operator names and slugs
    // console.log("Operator names found:", operatorNamesInSheet);
    // console.log("Generated slugs:", operatorSlugsInSheet);

    // Preload existing users by slug to find operators
    let existingUsers = await db.User.findAll({
      where: {
        slug: operatorSlugsInSheet,
      },
      attributes: ["id", "slug", "name"],
    });

    // If no users found by slug, try to find by name and update their slugs
    if (existingUsers.length === 0) {
      // console.log("No users found by slug, searching by name...");
      const usersByName = await db.User.findAll({
        where: {
          name: operatorNamesInSheet,
        },
        attributes: ["id", "slug", "name"],
      });

      // Update slugs for users that don't have them
      for (const user of usersByName) {
        if (!user.slug) {
          const newSlug = utils.generateSlug(user.name);
          await user.update({ slug: newSlug });
          user.slug = newSlug;
        }
      }
      existingUsers = usersByName;
    }

    // Debug: Log found users
    // console.log(
    //   "Found users:",
    //   existingUsers.map((u) => ({ id: u.id, name: u.name, slug: u.slug }))
    // );

    const userSlugMap = new Map();
    existingUsers.forEach((user) => {
      userSlugMap.set(user.slug, user);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const productionSheetNo = (
        row["Production Sheet No"] ||
        row.productionSheetNo ||
        row.ProductionSheetNo
      )
        ?.toString()
        .trim();
      const quantity = parseInt(row.Quantity || row.quantity);
      const operatorName = (
        row.Operator ||
        row.operator ||
        row["Operator Name"]
      )
        ?.toString()
        .trim();
      const vehicleType = (
        row["Vehicle type"] ||
        row.vehicleType ||
        row.VehicleType
      )
        ?.toString()
        .trim();
      const appointmentDate =
        row["Appointment date"] || row.appointmentDate || row.AppointmentDate;

      // Validate required fields
      if (!productionSheetNo) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Production Sheet No",
          data: row,
        });
        return;
      }

      if (!quantity || isNaN(quantity) || quantity < 1) {
        errorLog.push({
          row: index + 2,
          reason: "Invalid Quantity (must be a positive number)",
          data: row,
        });
        return;
      }

      if (!operatorName) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Operator name",
          data: row,
        });
        return;
      }

      if (!vehicleType) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Vehicle type",
          data: row,
        });
        return;
      }

      if (!appointmentDate) {
        errorLog.push({
          row: index + 2,
          reason: "Missing Appointment date",
          data: row,
        });
        return;
      }

      // Find operator by slug
      const operatorSlug = utils.generateSlug(operatorName);
      const operator = userSlugMap.get(operatorSlug);

      if (!operator) {
        errorLog.push({
          row: index + 2,
          reason: `Operator not found: ${operatorName}`,
          data: row,
        });
        return;
      }

      // Parse appointment date
      let parsedDate;
      try {
        let day, month, year;

        // Handle different date formats: DD-MM-YYYY or DD/MM/YYYY
        if (appointmentDate.includes("-")) {
          [day, month, year] = appointmentDate.split("-");
        } else if (appointmentDate.includes("/")) {
          [day, month, year] = appointmentDate.split("/");
        } else {
          throw new Error("Unsupported date format");
        }

        // Validate date parts
        if (!day || !month || !year) {
          throw new Error("Missing date components");
        }

        // Convert to numbers and validate ranges
        const dayNum = parseInt(day, 10);
        const monthNum = parseInt(month, 10);
        const yearNum = parseInt(year, 10);

        if (
          dayNum < 1 ||
          dayNum > 31 ||
          monthNum < 1 ||
          monthNum > 12 ||
          yearNum < 1900
        ) {
          throw new Error("Invalid date values");
        }

        // Create date in YYYY-MM-DD format for proper parsing
        parsedDate = new Date(
          `${yearNum}-${monthNum.toString().padStart(2, "0")}-${dayNum
            .toString()
            .padStart(2, "0")}`
        );

        if (isNaN(parsedDate.getTime())) {
          throw new Error("Invalid date format");
        }
      } catch (error) {
        errorLog.push({
          row: index + 2,
          reason: `Invalid Appointment date format: ${appointmentDate} (Expected DD-MM-YYYY or DD/MM/YYYY)`,
          data: row,
        });
        return;
      }

      // Prepare production data for insertion
      const productionData = {
        productionSheetNo: productionSheetNo,
        quantity: quantity,
        operatorId: operator.id,
        vehicleType: vehicleType,
        appointmentDate: parsedDate,
        status: "pending",
        createdBy: userId || null,
      };

      insertList.push(productionData);
    });

    // Initialize variables for results
    let insertedProductions = [];

    // Check for duplicate production sheet numbers in database
    if (insertList.length > 0) {
      const sheetNumbers = insertList.map((item) => item.productionSheetNo);
      const existingProductions = await db.Production.findAll({
        where: {
          productionSheetNo: sheetNumbers,
        },
        attributes: ["id", "productionSheetNo"],
      });

      const existingSheetNumbers = new Set(
        existingProductions.map((p) => p.productionSheetNo)
      );

      // Filter out duplicates and move them to skipped list
      const filteredInsertList = [];
      insertList.forEach((item, index) => {
        if (existingSheetNumbers.has(item.productionSheetNo)) {
          skippedList.push({
            row: index + 2,
            productionSheetNo: item.productionSheetNo,
            reason: `Duplicate Production Sheet No: ${item.productionSheetNo}`,
          });
        } else {
          filteredInsertList.push(item);
        }
      });

      // Perform bulk insert using transaction
      if (filteredInsertList.length > 0) {
        const transaction = await db.sequelize.transaction();
        try {
          insertedProductions = await db.Production.bulkCreate(
            filteredInsertList,
            {
              transaction,
              validate: true,
            }
          );
          await transaction.commit();
        } catch (error) {
          await transaction.rollback();
          throw new Error(`Bulk insert failed: ${error.message}`);
        }
      }
    }

    // Create rejected rows file (skipped + errors) with reasons (for all cases)
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return detailed results (always return, regardless of insertList length)
    return {
      totalRows: rows.length,
      insertedCount:
        insertList.length > 0
          ? insertedProductions
            ? insertedProductions.length
            : 0
          : 0,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertList.length > 0 ? insertedProductions || [] : [],
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    const rejectedData = rejectedRows.map((rejected) => ({
      ...originalRows[rejected.row - 2], // Get original row data
      REJECTION_REASON: rejected.reason,
    }));

    // Create new workbook with rejected data
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedData);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate filename for rejected file
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const rejectedFileName = `rejected_production_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return URL path (assuming uploads folder is served statically)
    return `/uploads/${rejectedFileName}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper
async function getSingleRecord(id) {
  const record = await db.Production.findByPk(id);
  if (!record) throw "Production record not found";
  return record;
}
