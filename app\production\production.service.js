const db = require("../_helpers/db");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  return await db.Production.findAll({
    where: {},
    include: [
      {
        model: db.User,
        as: "operator",
        attributes: ["id", "name", "email", "phone", "designation"],
      },
    ],
    order: [["appointmentDate", "DESC"]],
  });
}

async function getById(id) {
  const record = await db.Production.findByPk(id, {
    include: [
      {
        model: db.User,
        as: "operator",
        attributes: ["id", "name", "email", "phone", "designation"],
      },
    ],
  });
  if (!record) throw "Production record not found";
  return record;
}

async function create(params) {
  // Validate that operator exists
  const operator = await db.User.findByPk(params.operatorId);
  if (!operator) throw "Operator not found";

  return await db.Production.create(params);
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Validate that operator exists if operatorId is being updated
  if (params.operatorId) {
    const operator = await db.User.findByPk(params.operatorId);
    if (!operator) throw "Operator not found";
  }

  Object.assign(record, params);
  await record.save();
  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  // Soft delete
  record.status = "cancelled";
  record.deletedAt = new Date();
  await record.destroy();
}

// helper
async function getSingleRecord(id) {
  const record = await db.Production.findByPk(id);
  if (!record) throw "Production record not found";
  return record;
}
