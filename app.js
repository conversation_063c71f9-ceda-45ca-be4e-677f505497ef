const express = require("express");
const app = express();
const cors = require("cors");
const bodyParser = require("body-parser");
const Razorpay = require("razorpay");
const crypto = require("crypto");
const config = require("./config.json");

const path = require("path");
const errorHandler = require("./app/_middleware/error-handler");
global.__basedir = __dirname + "/";
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
// app.use(cors());
const corsOptions = {
  origin:
    config.environment === "FAT"
      ? "http://localhost:5173"
      : "https://mediafrontend.interosys.in/", // ✅ or your frontend domain
  credentials: true, // ✅ required if you're using cookies or Authorization headers
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"], // ✅ adjust based on headers you use
};

app.use(cors(corsOptions));
app.options("*", cors(corsOptions));

app.use("/api/users", require("./app/users/users.controller"));

// api routes
app.get("/", (req, res) => {
  res.json({ message: "Welcome to application." });
});

// Serve the uploads folder to access files by URL
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// global error handler
app.use(errorHandler);

// start server
const port =
  process.env.NODE_ENV === "production" ? process.env.PORT || 80 : 4000;
app.listen(port, () => console.log("Server listening on port " + port));
