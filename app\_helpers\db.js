const config = require("../../config.json");
const mysql = require("mysql2/promise");
const { Sequelize } = require("sequelize");

module.exports = db = {};

initialize();
async function initialize() {
  // create db if it doesn't already exist
  const environment = config.environment;
  let databaseValue = null;

  if (environment === "UAT") {
    databaseValue = config.fatDatabase;
  } else if (environment === "PROD") {
    databaseValue = config.prodDatabase;
  } else {
    databaseValue = config.database;
  }
  const { host, port, user, password, database } = databaseValue;
  // connect to dbs
  const sequelize = new Sequelize(database, user, password, {
    host: host,
    port: port,
    dialect: "mysql",
    pool: {
      max: 100,
      min: 0,
      idle: 5000,
      evict: 10000,
    },
  });

  sequelize
    .authenticate()
    .then(() => {
      console.log("Connection has been established successfully.");
    })
    .catch((error) => {
      console.error("Unable to connect to the database: ", error);
    });

  // init models and add them to the exported db object

  db.User = require("../users/user.model")(sequelize);
  db.Role = require("../roles/role.model")(sequelize);
  db.Production = require("../production/production.model")(sequelize);

  // Define associations
  db.Production.belongsTo(db.User, {
    foreignKey: "operatorId",
    as: "operator",
  });

  db.User.hasMany(db.Production, {
    foreignKey: "operatorId",
    as: "productions",
  });

  // sync all models with database
  await sequelize.sync();

  db.sequelize = sequelize;
}
