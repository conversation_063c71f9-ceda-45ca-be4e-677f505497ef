﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  authenticate,
};

async function getAll(params) {
  return await db.User.findAll({
    order: [["name", "ASC"]],
  });
}

async function authenticate({ email, password }) {
  const user = await db.User.scope("withHash").findOne({
    where: { email },
  });
  if (!user) throw "User not found in System";
  if (!(await bcrypt.compare(password, user.password))) {
    throw "Email or Password is incorrect";
  }

  if (user.status === "inactive") {
    throw "Your account is inactive. Please contact administrator.";
  }

  const token = jwt.sign({ sub: user.id }, config.secret, {
    expiresIn: "7d",
  });

  return {
    user: { ...omitHash(user.get()) },
    token,
  };
}

function omitHash(user) {
  const { password, resetToken, ...userWithoutHash } = user;
  return userWithoutHash;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // Generate slug from name
  if (params.name) {
    params.slug = utils.generateSlug(params.name);
  }

  return await db.User.create(params);
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Hash password if being updated
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // Generate slug from name if name is being updated
  if (params.name) {
    params.slug = utils.generateSlug(params.name);
  }

  Object.assign(record, params);
  await record.save();
  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  // Soft delete
  record.status = "inactive";
  record.deletedAt = new Date();
  await record.save();
}

// helper
async function getSingleRecord(id) {
  const record = await db.User.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
